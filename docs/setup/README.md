# Data Pipeline Setup Guide

This guide will help you set up and deploy the AWS-to-GCP data pipeline system.

## 🏗️ Architecture Overview

The system creates a temporary GCP Compute Engine VM that:
1. Connects to your AWS EC2 instance via SSH
2. Dumps the MariaDB/MySQL database
3. Processes and transforms the data
4. Uploads results to Google Cloud Storage
5. Self-destructs after completion

## 📋 Prerequisites

### Required Accounts and Services
- **Google Cloud Platform** account with billing enabled
- **GitHub** repository for code storage
- **AWS EC2** instance with MariaDB/MySQL database
- SSH access to the AWS EC2 instance

### Required Tools (for local development)
- [Terraform](https://www.terraform.io/downloads) >= 1.0
- [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
- [Git](https://git-scm.com/downloads)

## 🔧 Initial Setup

### 1. GCP Project Setup

1. Create a new GCP project or use an existing one
2. Enable required APIs:
   ```bash
   gcloud services enable compute.googleapis.com
   gcloud services enable storage.googleapis.com
   gcloud services enable logging.googleapis.com
   gcloud services enable monitoring.googleapis.com
   ```

3. Create a service account for Terraform:
   ```bash
   gcloud iam service-accounts create terraform-sa \
     --display-name="Terraform Service Account"
   
   gcloud projects add-iam-policy-binding YOUR_PROJECT_ID \
     --member="serviceAccount:terraform-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com" \
     --role="roles/editor"
   
   gcloud iam service-accounts keys create terraform-sa-key.json \
     --iam-account=terraform-sa@YOUR_PROJECT_ID.iam.gserviceaccount.com
   ```

### 2. GitHub Repository Setup

1. Fork or clone this repository
2. Set up GitHub Secrets (Settings → Secrets and variables → Actions):

   **Required Secrets:**
   - `GCP_SA_KEY`: Content of the `terraform-sa-key.json` file
   - `GCP_PROJECT_ID`: Your GCP project ID
   - `GCP_REGION`: GCP region (e.g., `us-central1`)
   - `GCP_ZONE`: GCP zone (e.g., `us-central1-a`)
   - `AWS_PRIVATE_KEY`: Your AWS EC2 private key content
   - `AWS_PUBLIC_KEY`: Your AWS EC2 public key content
   - `AWS_HOSTNAME`: Your AWS EC2 IP address (e.g., `***********`)
   - `AWS_USER`: Your AWS EC2 username (e.g., `forge`)

   **Optional Secrets:**
   - `VM_MACHINE_TYPE`: GCP VM machine type (default: `e2-standard-4`)

### 3. SSH Key Setup

Ensure you have SSH key pair for AWS EC2 access:

1. **If you already have keys:**
   - Copy the private key content to `AWS_PRIVATE_KEY` secret
   - Copy the public key content to `AWS_PUBLIC_KEY` secret

2. **If you need to create new keys:**
   ```bash
   ssh-keygen -t rsa -b 4096 -f aws_ec2_key
   # Add the public key to your AWS EC2 instance's ~/.ssh/authorized_keys
   ```

## 🚀 Deployment Options

### Option 1: GitHub Actions (Recommended)

#### Deploy Infrastructure
1. Go to your GitHub repository
2. Navigate to **Actions** tab
3. Select **Deploy Data Pipeline Infrastructure**
4. Click **Run workflow**
5. Choose environment (dev/staging/prod)
6. Select action (plan/apply/destroy)

#### Run Pipeline
1. Navigate to **Actions** tab
2. Select **Run Data Pipeline**
3. Click **Run workflow**
4. Choose environment and options

### Option 2: Local Deployment

#### Prerequisites
```bash
# Install Terraform
# Install Google Cloud SDK
gcloud auth login
gcloud config set project YOUR_PROJECT_ID
```

#### Deploy
```bash
cd infrastructure/terraform

# Copy and edit configuration
cp ../../config/environments/dev/terraform.tfvars.template terraform.tfvars
# Edit terraform.tfvars with your actual values

# Initialize Terraform
terraform init

# Plan deployment
terraform plan

# Apply deployment
terraform apply
```

## 📊 Monitoring and Logs

### View VM Startup Logs
```bash
gcloud compute instances get-serial-port-output VM_NAME \
  --zone=VM_ZONE \
  --project=PROJECT_ID
```

### SSH to VM (for debugging)
```bash
gcloud compute ssh VM_NAME \
  --zone=VM_ZONE \
  --project=PROJECT_ID
```

### View Cloud Logs
```bash
gcloud logging read "resource.type=gce_instance" \
  --limit=50 \
  --format="table(timestamp,severity,textPayload)"
```

## 🔍 Troubleshooting

### Common Issues

1. **SSH Connection Failed**
   - Verify AWS EC2 security groups allow SSH from GCP IP ranges
   - Check that the private key format is correct
   - Ensure the AWS EC2 instance is running

2. **Terraform State Issues**
   - Check if the GCS bucket for Terraform state exists
   - Verify service account permissions

3. **VM Startup Timeout**
   - Check VM startup logs for errors
   - Verify all required secrets are set correctly

4. **Permission Denied**
   - Verify GCP service account has required permissions
   - Check IAM roles and policies

### Debug Commands

```bash
# Check VM status
gcloud compute instances list

# View detailed VM information
gcloud compute instances describe VM_NAME --zone=VM_ZONE

# Check firewall rules
gcloud compute firewall-rules list

# View service account permissions
gcloud projects get-iam-policy PROJECT_ID
```

## 📝 Configuration Files

- **Terraform Variables**: `config/environments/{env}/terraform.tfvars.template`
- **GitHub Workflows**: `.github/workflows/`
- **Startup Script**: `infrastructure/terraform/startup-script.sh`

## 🔒 Security Best Practices

1. **Never commit sensitive data** to the repository
2. **Use GitHub Secrets** for all credentials
3. **Rotate SSH keys** regularly
4. **Monitor GCP billing** and set up alerts
5. **Review firewall rules** periodically
6. **Enable audit logging** in GCP

## 📞 Support

For issues and questions:
1. Check the troubleshooting section above
2. Review GitHub Actions logs
3. Check GCP Cloud Logging
4. Create an issue in the repository

## 🔄 Pipeline Schedule

The pipeline runs automatically based on the configured schedule:
- **Development**: Manual trigger only
- **Staging**: Daily for testing
- **Production**: Weekly (Sunday 2 AM UTC)

You can also trigger the pipeline manually through GitHub Actions.
